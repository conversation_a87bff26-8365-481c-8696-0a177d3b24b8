import { jsPDF } from 'jspdf';
import * as docx from 'docx';
import J<PERSON><PERSON><PERSON> from 'jszip';

// Database client will be imported at runtime
import type { Chapter, Character, Project, StoryBible } from '@/lib/db/types';

// Extended project interface for export purposes
interface ExportProject extends Project {
  author_name?: string
  isbn?: string  
  publisher?: string
  language?: string
}

// Database client interface
interface DatabaseClient {
  projects: {
    getById: (id: string) => Promise<Project | null>;
  };
  chapters: {
    getAll: (projectId: string) => Promise<Chapter[]>;
  };
  characters: {
    getAll: (projectId: string) => Promise<Character[]>;
  };
  storyBible: {
    getAll: (projectId: string) => Promise<StoryBible[]>;
  };
}

let db: DatabaseClient | null = null;

export interface ExportOptions {
  format: 'pdf' | 'epub' | 'docx' | 'txt' | 'markdown';
  includeMetadata: boolean;
  includeFrontMatter: boolean;
  includeChapterBreaks: boolean;
  includeTableOfContents: boolean;
  customStyling?: {
    fontFamily?: string;
    fontSize?: number;
    lineSpacing?: number;
    margins?: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  };
  pageFormat?: 'letter' | 'a4' | 'custom';
  chapterSelection?: string[]; // Chapter IDs to include
}

export interface ProjectExportData {
  project: ExportProject;
  chapters: Chapter[];
  characters: Character[];
  storyBible: StoryBible[];
  settings: Record<string, unknown>;
}

class ExportService {
  async exportProject(projectId: string, options: ExportOptions): Promise<Blob> {
    // Fetch all project data
    const exportData = await this.gatherProjectData(projectId, options);
    
    switch (options.format) {
      case 'pdf':
        return await this.exportToPDF(exportData, options);
      case 'docx':
        return await this.exportToDocx(exportData, options);
      case 'epub':
        return await this.exportToEpub(exportData, options);
      case 'txt':
        return await this.exportToText(exportData, options);
      case 'markdown':
        return await this.exportToMarkdown(exportData, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  private async gatherProjectData(projectId: string, options: ExportOptions): Promise<ProjectExportData> {
    if (!db) {
      try {
        const { db: dbClient } = await import('../db/client');
        db = dbClient;
      } catch {
        throw new Error('Database client not available in this context');
      }
    }

    try {
      const [project, allChapters, characters, storyBible] = await Promise.all([
        db.projects.getById(projectId),
        db.chapters.getAll(projectId),
        db.characters.getAll(projectId),
        db.storyBible.getAll(projectId)
      ]);

      if (!project) {
        throw new Error(`Project with ID ${projectId} not found`);
      }

      // Filter chapters if specific selection is provided
      const chapters = options.chapterSelection 
        ? allChapters.filter((ch: Chapter) => options.chapterSelection!.includes(ch.id))
        : allChapters || [];

      // Sort chapters by chapter number
      chapters.sort((a: Chapter, b: Chapter) => a.chapter_number - b.chapter_number);

      return {
        project,
        chapters,
        characters: characters || [],
        storyBible: storyBible || [],
        settings: {}
      };
    } catch (error) {
      console.error('Error gathering project data:', error);
      throw new Error(`Failed to gather project data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async exportToPDF(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: options.pageFormat === 'a4' ? 'a4' : 'letter'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margins = options.customStyling?.margins || { top: 20, bottom: 20, left: 20, right: 20 };
    const contentWidth = pageWidth - margins.left - margins.right;

    let yPosition = margins.top;

    // Title Page
    if (options.includeFrontMatter) {
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      const titleLines = pdf.splitTextToSize(data.project.title, contentWidth);
      pdf.text(titleLines, margins.left, yPosition);
      yPosition += titleLines.length * 10;

      if (data.project.description) {
        yPosition += 10;
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        const descLines = pdf.splitTextToSize(data.project.description, contentWidth);
        pdf.text(descLines, margins.left, yPosition);
        yPosition += descLines.length * 6;
      }

      // Metadata
      if (options.includeMetadata) {
        yPosition += 20;
        pdf.setFontSize(10);
        pdf.text(`Genre: ${data.project.primary_genre || 'Not specified'}`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Chapters: ${data.chapters.length}`, margins.left, yPosition);
        yPosition += 6;
        pdf.text(`Created: ${new Date(data.project.created_at).toLocaleDateString()}`, margins.left, yPosition);
      }

      pdf.addPage();
      yPosition = margins.top;
    }

    // Table of Contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Table of Contents', margins.left, yPosition);
      yPosition += 15;

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      
      data.chapters.forEach((chapter) => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        pdf.text(`${chapter.chapter_number}. ${title}`, margins.left + 5, yPosition);
        yPosition += 8;

        if (yPosition > pageHeight - margins.bottom) {
          pdf.addPage();
          yPosition = margins.top;
        }
      });

      pdf.addPage();
      yPosition = margins.top;
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        pdf.addPage();
        yPosition = margins.top;
      }

      // Chapter Title
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      pdf.text(chapterTitle, margins.left, yPosition);
      yPosition += 20;

      // Chapter Content
      if (chapter.content) {
        pdf.setFontSize(options.customStyling?.fontSize || 11);
        pdf.setFont('helvetica', 'normal');
        
        const lines = pdf.splitTextToSize(chapter.content, contentWidth) as string[];
        const lineHeight = options.customStyling?.lineSpacing || 6;

        lines.forEach((line: string) => {
          if (yPosition > pageHeight - margins.bottom - lineHeight) {
            pdf.addPage();
            yPosition = margins.top;
          }

          pdf.text(line, margins.left, yPosition);
          yPosition += lineHeight;
        });
      }

      yPosition += 10; // Space after chapter
    });

    return new Blob([pdf.output('blob')], { type: 'application/pdf' });
  }

  private async exportToDocx(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    const sections: docx.ISectionOptions[] = [];

    // Title page section
    if (options.includeFrontMatter) {
      sections.push({
        properties: {
          page: {
            margin: {
              top: docx.convertInchesToTwip(1),
              bottom: docx.convertInchesToTwip(1),
              left: docx.convertInchesToTwip(1),
              right: docx.convertInchesToTwip(1),
            },
          },
        },
        children: [
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: data.project.title,
                size: 48,
                bold: true,
              }),
            ],
            alignment: docx.AlignmentType.CENTER,
            spacing: { after: 400 },
          }),
          ...(data.project.description ? [
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: data.project.description,
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
          ] : []),
          ...(options.includeMetadata ? [
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Genre: ${data.project.primary_genre || 'Not specified'}`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `Created: ${new Date(data.project.created_at).toLocaleDateString()}`,
                  size: 20,
                }),
              ],
              spacing: { after: 200 },
            }),
          ] : []),
        ],
      });
    }

    // Table of Contents
    if (options.includeTableOfContents) {
      const tocChildren = [
        new docx.Paragraph({
          children: [
            new docx.TextRun({
              text: 'Table of Contents',
              size: 32,
              bold: true,
            }),
          ],
          spacing: { after: 400 },
        }),
        ...data.chapters.map(chapter => 
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: `${chapter.chapter_number}. ${chapter.title || `Chapter ${chapter.chapter_number}`}`,
                size: 22,
              }),
            ],
            spacing: { after: 200 },
          })
        ),
      ];

      sections.push({
        properties: {
          page: {
            margin: {
              top: docx.convertInchesToTwip(1),
              bottom: docx.convertInchesToTwip(1),
              left: docx.convertInchesToTwip(1),
              right: docx.convertInchesToTwip(1),
            },
          },
        },
        children: tocChildren,
      });
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      const chapterChildren = [
        new docx.Paragraph({
          children: [
            new docx.TextRun({
              text: chapter.title || `Chapter ${chapter.chapter_number}`,
              size: 32,
              bold: true,
            }),
          ],
          spacing: { after: 400 },
          pageBreakBefore: options.includeChapterBreaks && index > 0,
        }),
      ];

      if (chapter.content) {
        // Split content into paragraphs
        const paragraphs = chapter.content.split('\n\n').filter(p => p.trim());
        
        paragraphs.forEach(paragraphText => {
          chapterChildren.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: paragraphText.trim(),
                  size: (options.customStyling?.fontSize || 11) * 2, // Word uses half-points
                }),
              ],
              spacing: { 
                after: 240,
                line: options.customStyling?.lineSpacing ? options.customStyling.lineSpacing * 20 : 276,
              },
            })
          );
        });
      }

      sections.push({
        properties: {
          page: {
            margin: {
              top: docx.convertInchesToTwip(options.customStyling?.margins?.top || 1),
              bottom: docx.convertInchesToTwip(options.customStyling?.margins?.bottom || 1),
              left: docx.convertInchesToTwip(options.customStyling?.margins?.left || 1),
              right: docx.convertInchesToTwip(options.customStyling?.margins?.right || 1),
            },
          },
        },
        children: chapterChildren,
      });
    });

    // Create document with all sections
    const doc = new docx.Document({
      creator: 'BookScribe AI',
      title: data.project.title,
      description: data.project.description || '',
      sections: sections
    });

    return await docx.Packer.toBlob(doc);
  }

  private async exportToEpub(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    const zip = new JSZip();
    
    // EPUB structure
    zip.file('mimetype', 'application/epub+zip');
    
    const metaInf = zip.folder('META-INF');
    metaInf!.file('container.xml', `<?xml version="1.0" encoding="UTF-8"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>`);

    const oebps = zip.folder('OEBPS');
    // const images = oebps!.folder('images'); // Reserved for future image support
    
    // Package file
    const packageContent = this.generateEpubPackage(data, options);
    oebps!.file('content.opf', packageContent);
    
    // NCX file for backwards compatibility
    const ncxContent = this.generateEpubNCX(data, options);
    oebps!.file('toc.ncx', ncxContent);
    
    // Navigation
    const navContent = this.generateEpubNavigation(data, options);
    oebps!.file('nav.xhtml', navContent);
    
    // Cover page
    if (data.project.cover_image_url) {
      const coverContent = this.generateEpubCoverPage(data);
      oebps!.file('cover.xhtml', coverContent);
      // Note: In production, you'd fetch and include the actual cover image
      // images!.file('cover.jpg', coverImageBlob);
    }
    
    // Title page
    if (options.includeFrontMatter) {
      const titleContent = this.generateEpubTitlePage(data, options);
      oebps!.file('title.xhtml', titleContent);
      
      // Copyright page
      const copyrightContent = this.generateEpubCopyrightPage(data);
      oebps!.file('copyright.xhtml', copyrightContent);
      
      // Dedication page
      if (data.project.dedication) {
        const dedicationContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Dedication</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="dedication">
    <p>${this.escapeXml(data.project.dedication)}</p>
  </div>
</body>
</html>`;
        oebps!.file('dedication.xhtml', dedicationContent);
      }
    }
    
    // Table of Contents
    if (options.includeTableOfContents) {
      const tocContent = this.generateEpubTableOfContents(data);
      oebps!.file('toc.xhtml', tocContent);
    }
    
    // Chapters with enhanced content processing
    data.chapters.forEach((chapter) => {
      const processedContent = chapter.content ? this.processChapterContent(chapter.content) : '<p>Content coming soon...</p>';
      const chapterContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${this.escapeXml(chapter.title || `Chapter ${chapter.chapter_number}`)}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="chapter">
    <h1>${this.escapeXml(chapter.title || `Chapter ${chapter.chapter_number}`)}</h1>
    <div class="content">
      ${processedContent}
    </div>
  </div>
</body>
</html>`;
      oebps!.file(`chapter-${chapter.chapter_number}.xhtml`, chapterContent);
    });
    
    // Acknowledgments
    if (data.project.acknowledgments) {
      const acknowledgmentsContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Acknowledgments</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="acknowledgments">
    <h1>Acknowledgments</h1>
    <p>${this.escapeXml(data.project.acknowledgments)}</p>
  </div>
</body>
</html>`;
      oebps!.file('acknowledgments.xhtml', acknowledgmentsContent);
    }
    
    // Author bio
    if (data.project.author_bio) {
      const authorContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>About the Author</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="author-bio">
    <h1>About the Author</h1>
    <p>${this.escapeXml(data.project.author_bio)}</p>
  </div>
</body>
</html>`;
      oebps!.file('author.xhtml', authorContent);
    }
    
    // CSS
    const cssContent = this.generateEpubCSS(options);
    oebps!.file('styles.css', cssContent);

    return await zip.generateAsync({ type: 'blob' });
  }

  private async exportToText(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    let content = '';

    // Title and metadata
    if (options.includeFrontMatter) {
      content += `${data.project.title}\n`;
      content += '='.repeat(data.project.title.length) + '\n\n';
      
      if (data.project.description) {
        content += `${data.project.description}\n\n`;
      }

      if (options.includeMetadata) {
        content += `Genre: ${data.project.primary_genre || 'Not specified'}\n`;
        content += `Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words\n`;
        content += `Chapters: ${data.chapters.length}\n`;
        content += `Created: ${new Date(data.project.created_at).toLocaleDateString()}\n\n`;
      }
    }

    // Table of contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      content += 'Table of Contents\n';
      content += '-'.repeat(17) + '\n\n';
      
      data.chapters.forEach(chapter => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        content += `${chapter.chapter_number}. ${title}\n`;
      });
      content += '\n\n';
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        content += '\n' + '='.repeat(50) + '\n\n';
      }

      const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      content += `${chapterTitle}\n`;
      content += '-'.repeat(chapterTitle.length) + '\n\n';

      if (chapter.content) {
        content += chapter.content + '\n\n';
      }
    });

    return new Blob([content], { type: 'text/plain;charset=utf-8' });
  }

  private async exportToMarkdown(data: ProjectExportData, options: ExportOptions): Promise<Blob> {
    let content = '';

    // Title and metadata
    if (options.includeFrontMatter) {
      content += `# ${data.project.title}\n\n`;
      
      if (data.project.description) {
        content += `${data.project.description}\n\n`;
      }

      if (options.includeMetadata) {
        content += `**Genre:** ${data.project.primary_genre || 'Not specified'}  \n`;
        content += `**Word Count:** ${data.project.current_word_count?.toLocaleString() || '0'} words  \n`;
        content += `**Chapters:** ${data.chapters.length}  \n`;
        content += `**Created:** ${new Date(data.project.created_at).toLocaleDateString()}  \n\n`;
      }
    }

    // Table of contents
    if (options.includeTableOfContents && data.chapters.length > 0) {
      content += '## Table of Contents\n\n';
      
      data.chapters.forEach(chapter => {
        const title = chapter.title || `Chapter ${chapter.chapter_number}`;
        const anchor = title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
        content += `${chapter.chapter_number}. [${title}](#${anchor})\n`;
      });
      content += '\n';
    }

    // Chapters
    data.chapters.forEach((chapter, index) => {
      if (options.includeChapterBreaks && index > 0) {
        content += '\n---\n\n';
      }

      const chapterTitle = chapter.title || `Chapter ${chapter.chapter_number}`;
      content += `## ${chapterTitle}\n\n`;

      if (chapter.content) {
        content += chapter.content + '\n\n';
      }
    });

    return new Blob([content], { type: 'text/markdown;charset=utf-8' });
  }

  // EPUB helper methods
  private generateEpubPackage(data: ProjectExportData, options: ExportOptions): string {
    const manifestItems = [
      '<item id="nav" href="nav.xhtml" media-type="application/xhtml+xml" properties="nav"/>',
      '<item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>',
      '<item id="css" href="styles.css" media-type="text/css"/>',
    ];

    const spineItems = [];

    // Add cover page if exists
    if (data.project.cover_image_url) {
      manifestItems.push('<item id="cover" href="cover.xhtml" media-type="application/xhtml+xml"/>');
      manifestItems.push('<item id="cover-image" href="images/cover.jpg" media-type="image/jpeg" properties="cover-image"/>');
      spineItems.push('<itemref idref="cover"/>');
    }

    if (options.includeFrontMatter) {
      manifestItems.push('<item id="title" href="title.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="title"/>');
      
      // Add dedication page if exists
      if (data.project.dedication) {
        manifestItems.push('<item id="dedication" href="dedication.xhtml" media-type="application/xhtml+xml"/>');
        spineItems.push('<itemref idref="dedication"/>');
      }
      
      // Add copyright page
      manifestItems.push('<item id="copyright" href="copyright.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="copyright"/>');
    }

    // Add table of contents
    if (options.includeTableOfContents) {
      manifestItems.push('<item id="toc" href="toc.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="toc"/>');
    }

    data.chapters.forEach(chapter => {
      manifestItems.push(`<item id="chapter-${chapter.chapter_number}" href="chapter-${chapter.chapter_number}.xhtml" media-type="application/xhtml+xml"/>`);
      spineItems.push(`<itemref idref="chapter-${chapter.chapter_number}"/>`);
    });

    // Add acknowledgments if exists
    if (data.project.acknowledgments) {
      manifestItems.push('<item id="acknowledgments" href="acknowledgments.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="acknowledgments"/>');
    }

    // Add author bio if exists
    if (data.project.author_bio) {
      manifestItems.push('<item id="author" href="author.xhtml" media-type="application/xhtml+xml"/>');
      spineItems.push('<itemref idref="author"/>');
    }

    return `<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" version="3.0" unique-identifier="uid">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:identifier id="uid">${data.project.id}</dc:identifier>
    <dc:title>${data.project.title}</dc:title>
    <dc:language>${data.project.language || 'en'}</dc:language>
    <dc:creator>${data.project.author_name || 'Unknown Author'}</dc:creator>
    <dc:publisher>${data.project.publisher || 'BookScribe AI'}</dc:publisher>
    <dc:date>${new Date().toISOString().split('T')[0]}</dc:date>
    ${data.project.description ? `<dc:description>${this.escapeXml(data.project.description)}</dc:description>` : ''}
    ${data.project.primary_genre ? `<dc:subject>${data.project.primary_genre}</dc:subject>` : ''}
    ${data.project.subgenre ? `<dc:subject>${data.project.subgenre}</dc:subject>` : ''}
    ${data.project.isbn ? `<dc:identifier opf:scheme="ISBN">${data.project.isbn}</dc:identifier>` : ''}
    ${data.project.cover_image_url ? '<meta name="cover" content="cover-image"/>' : ''}
  </metadata>
  <manifest>
    ${manifestItems.join('\n    ')}
  </manifest>
  <spine toc="ncx">
    ${spineItems.join('\n    ')}
  </spine>
  <guide>
    ${data.project.cover_image_url ? '<reference type="cover" title="Cover" href="cover.xhtml"/>' : ''}
    ${options.includeTableOfContents ? '<reference type="toc" title="Table of Contents" href="toc.xhtml"/>' : ''}
    <reference type="text" title="Start" href="chapter-1.xhtml"/>
  </guide>
</package>`;
  }

  private generateEpubNavigation(data: ProjectExportData, options: ExportOptions): string {
    let navItems = '';
    
    if (options.includeFrontMatter) {
      navItems += '<li><a href="title.xhtml">Title Page</a></li>';
    }

    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      navItems += `<li><a href="chapter-${chapter.chapter_number}.xhtml">${title}</a></li>`;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
  <title>Navigation</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <nav epub:type="toc">
    <h1>Table of Contents</h1>
    <ol>
      ${navItems}
    </ol>
  </nav>
</body>
</html>`;
  }

  private generateEpubTitlePage(data: ProjectExportData, options: ExportOptions): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${data.project.title}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="title-page">
    <h1>${data.project.title}</h1>
    ${data.project.description ? `<p class="description">${data.project.description}</p>` : ''}
    ${options.includeMetadata ? `
    <div class="metadata">
      <p>Genre: ${data.project.primary_genre || 'Not specified'}</p>
      <p>Word Count: ${data.project.current_word_count?.toLocaleString() || '0'} words</p>
      <p>Created: ${new Date(data.project.created_at).toLocaleDateString()}</p>
    </div>` : ''}
  </div>
</body>
</html>`;
  }

  private _generateEpubChapter(chapter: { chapter_number: number; title?: string; content?: string }): string {
    const title = chapter.title || `Chapter ${chapter.chapter_number}`;
    const content = chapter.content || '';
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>${title}</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="chapter">
    <h1>${title}</h1>
    <div class="content">
      ${content ? content.split('\n\n').map((p: string) => `<p>${p.trim()}</p>`).join('\n      ') : '<p>Content coming soon...</p>'}
    </div>
  </div>
</body>
</html>`;
  }

  private generateEpubCSS(options: ExportOptions): string {
    const fontSize = options.customStyling?.fontSize || 16;
    const lineHeight = options.customStyling?.lineSpacing || 1.5;
    const fontFamily = options.customStyling?.fontFamily || 'serif';

    return `
body {
  font-family: ${fontFamily};
  font-size: ${fontSize}px;
  line-height: ${lineHeight};
  margin: 2em;
}

.title-page {
  text-align: center;
  margin-top: 4em;
}

.title-page h1 {
  font-size: 2.5em;
  margin-bottom: 1em;
}

.title-page .description {
  font-size: 1.2em;
  font-style: italic;
  margin-bottom: 2em;
}

.title-page .metadata {
  margin-top: 3em;
  font-size: 0.9em;
  color: #666;
}

.chapter h1 {
  font-size: 2em;
  margin-bottom: 1em;
  page-break-before: always;
}

.chapter p {
  margin-bottom: 1em;
  text-indent: 1.5em;
}

.chapter p:first-of-type {
  text-indent: 0;
}

/* Drop caps for first letter */
.chapter p:first-of-type::first-letter {
  font-size: 3em;
  line-height: 1;
  float: left;
  margin: 0 0.1em 0 0;
}

/* Scene breaks */
.scene-break {
  text-align: center;
  margin: 2em 0;
  font-size: 1.5em;
}

/* Blockquotes */
blockquote {
  margin: 1em 2em;
  font-style: italic;
  border-left: 3px solid #ccc;
  padding-left: 1em;
}

/* Footnotes */
.footnote {
  font-size: 0.85em;
  vertical-align: super;
}

.footnotes {
  margin-top: 3em;
  padding-top: 1em;
  border-top: 1px solid #ccc;
  font-size: 0.9em;
}
`;
  }

  // Additional EPUB helper methods for advanced features
  private generateEpubNCX(data: ProjectExportData, options: ExportOptions): string {
    let navPoints = '';
    let playOrder = 1;

    if (data.project.cover_image_url) {
      navPoints += `
    <navPoint id="cover" playOrder="${playOrder++}">
      <navLabel><text>Cover</text></navLabel>
      <content src="cover.xhtml"/>
    </navPoint>`;
    }

    if (options.includeFrontMatter) {
      navPoints += `
    <navPoint id="title" playOrder="${playOrder++}">
      <navLabel><text>Title Page</text></navLabel>
      <content src="title.xhtml"/>
    </navPoint>`;
    }

    if (options.includeTableOfContents) {
      navPoints += `
    <navPoint id="toc" playOrder="${playOrder++}">
      <navLabel><text>Table of Contents</text></navLabel>
      <content src="toc.xhtml"/>
    </navPoint>`;
    }

    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      navPoints += `
    <navPoint id="chapter-${chapter.chapter_number}" playOrder="${playOrder++}">
      <navLabel><text>${title}</text></navLabel>
      <content src="chapter-${chapter.chapter_number}.xhtml"/>
    </navPoint>`;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE ncx PUBLIC "-//NISO//DTD ncx 2005-1//EN" "http://www.daisy.org/z3986/2005/ncx-2005-1.dtd">
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
  <head>
    <meta name="dtb:uid" content="${data.project.id}"/>
    <meta name="dtb:depth" content="1"/>
    <meta name="dtb:totalPageCount" content="0"/>
    <meta name="dtb:maxPageNumber" content="0"/>
  </head>
  <docTitle>
    <text>${this.escapeXml(data.project.title)}</text>
  </docTitle>
  <navMap>
    ${navPoints}
  </navMap>
</ncx>`;
  }

  private generateEpubCoverPage(data: ProjectExportData): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Cover</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
  <style>
    body { margin: 0; padding: 0; }
    .cover { width: 100%; height: 100vh; text-align: center; }
    .cover img { max-width: 100%; max-height: 100%; }
  </style>
</head>
<body>
  <div class="cover">
    <img src="images/cover.jpg" alt="${this.escapeXml(data.project.title)} Cover"/>
  </div>
</body>
</html>`;
  }

  private generateEpubCopyrightPage(data: ProjectExportData): string {
    const year = new Date().getFullYear();
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Copyright</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="copyright">
    <p>Copyright © ${year} ${data.project.author_name || 'Author'}</p>
    <p>All rights reserved.</p>
    ${data.project.isbn ? `<p>ISBN: ${data.project.isbn}</p>` : ''}
    <p>Published by ${data.project.publisher || 'BookScribe AI'}</p>
    <p>This is a work of fiction. Names, characters, places, and incidents either are the product of the author's imagination or are used fictitiously. Any resemblance to actual persons, living or dead, events, or locales is entirely coincidental.</p>
  </div>
</body>
</html>`;
  }

  private generateEpubTableOfContents(data: ProjectExportData): string {
    let tocItems = '';
    
    data.chapters.forEach(chapter => {
      const title = chapter.title || `Chapter ${chapter.chapter_number}`;
      tocItems += `<li><a href="chapter-${chapter.chapter_number}.xhtml">${this.escapeXml(title)}</a></li>\n      `;
    });

    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Table of Contents</title>
  <link rel="stylesheet" type="text/css" href="styles.css"/>
</head>
<body>
  <div class="toc">
    <h1>Table of Contents</h1>
    <ol>
      ${tocItems}
    </ol>
  </div>
</body>
</html>`;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  private processChapterContent(content: string): string {
    // Convert markdown-style formatting to HTML
    return content
      // Scene breaks (*** or ---)
      .replace(/^(\*\*\*|---)$/gm, '<div class="scene-break">* * *</div>')
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Paragraphs
      .split('\n\n')
      .map(p => p.trim())
      .filter(p => p.length > 0)
      .map(p => {
        if (p.startsWith('<div')) return p; // Already HTML
        return `<p>${p}</p>`;
      })
      .join('\n      ');
  }
}

export const exportService = new ExportService();