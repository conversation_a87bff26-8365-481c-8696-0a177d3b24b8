{"permissions": {"allow": ["Bash(npx create-next-app:*)", "Bash(npx --yes create-next-app@latest . --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npx --yes create-next-app@latest bookscribe-app --typescript --tailwind --app --src-dir --import-alias=\"@/*\" --eslint)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx shadcn@latest add:*)", "Bash(npm run build:*)", "Bash(npx next:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(true)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(touch:*)", "Bash(for step in structure-pacing-step character-world-step themes-content-step technical-specs-step review-step)", "Bash(do)", "Bash(done)", "Bash(rg:*)", "Bash(cp:*)", "Bash(/dev/null)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm:*)", "Bash(node:*)", "Bash(npx ts-node -e \"\nimport { runSchemaTests } from './src/lib/validation/test-schemas';\nrunSchemaTests();\n\")", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(npx madge:*)", "Bash(NODE_OPTIONS='--max-old-space-size=8192' timeout 30s npx next build --debug 2 >& 1)", "<PERSON><PERSON>(mv:*)", "Bash(npx depcheck:*)", "Bash(awk:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(echo)", "Bash(for file in character-development-grid.tsx development-dimension-selector.tsx arc-prediction-panel.tsx character-arc-visualizer.tsx)", "Bash(do echo \"- src/components/analysis/$file\")", "Bash(git reset:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/mnt/c/Users/<USER>/BookScribe/fix-type-imports.sh:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(NODE_OPTIONS=--max-old-space-size=4096 npx next build)", "WebFetch(domain:supabase.com)", "Bash(NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 npx next build --debug 2 >& 1)", "<PERSON><PERSON>(killall:*)", "Bash(for file in src/lib/services/ai-orchestrator.ts src/lib/services/content-generator.ts src/lib/services/semantic-search.ts src/lib/supabase/middleware.ts src/lib/auth/server.ts)", "Bash(do echo \"=== $file ===\")", "Bash(for file in src/app/api/subscription/portal/route.ts src/components/wizard/wizard-wrapper.tsx src/components/editor/editor-wrapper.tsx src/components/dashboard/dashboard-wrapper.tsx src/app/error.tsx)", "Bash(for:*)", "Bash(wc:*)", "<PERSON><PERSON>(python3:*)", "Bash(npx @axe-core/cli:*)", "Bash(PORT=3000 npm run dev 2 >& 1)", "Bash(NEXT_CACHE_DISABLED=1 npm run dev)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://xvqeiwrpbzpiqvwuvtpj.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2cWVpd3JwYnpwaXF2d3V2dHBqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTc2MDAyNiwiZXhwIjoyMDY1MzM2MDI2fQ.X68h4b6kdZaLzKoBy7DV8XBmEpRIexErTrBIS-khjko node -e \"\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);\n\nasync function run() {\n  console.log('🔄 Adding fields to profiles table...');\n  \n  const queries = [\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location VARCHAR(255);',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS website VARCHAR(500);',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS writing_goals JSONB DEFAULT \\\\'{\\\\\"daily_words\\\\\": 1000, \\\\\"weekly_hours\\\\\": 10, \\\\\"genre_focus\\\\\": \\\\\"Fiction\\\\\"}\\\\';',\n    'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT \\\\'{\\\\\"public_profile\\\\\": true, \\\\\"email_notifications\\\\\": true, \\\\\"writing_reminders\\\\\": true, \\\\\"beta_features\\\\\": false}\\\\';',\n    'CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);'\n  ];\n  \n  for (const [i, query] of queries.entries()) {\n    console.log(\\`Query \\${i+1}/\\${queries.length}...\\`);\n    const { error } = await supabase.rpc('exec_sql', { sql: query });\n    if (error) console.warn('Warning:', error.message);\n    else console.log('✅ Success');\n  }\n  \n  console.log('\\\\n🧪 Testing...');\n  const { data, error } = await supabase.from('profiles').select('username, bio, writing_goals').limit(1);\n  if (error) console.warn('Test warning:', error.message);\n  else console.log('✅ Schema update complete!');\n}\n\nrun();\n\")", "WebFetch(domain:stripe.com)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:mcp.so)", "WebFetch(domain:mcp.so)", "<PERSON><PERSON>(claude mcp:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(npx playwright:*)", "mcp__puppeteer__puppeteer_navigate", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(npx cross-env:*)"], "deny": []}}