import { openai } from '../openai';

export interface MemoryChunk {
  id: string;
  type: 'character' | 'plot' | 'setting' | 'relationship' | 'theme' | 'event';
  content: string;
  importance: number; // 0-1 scale
  lastAccessed: Date;
  accessCount: number;
  chapters: number[];
  keywords: string[];
  embedding?: number[];
  dependencies: string[]; // IDs of related chunks
}

export interface ContextWindow {
  activeChunks: MemoryChunk[];
  totalTokens: number;
  maxTokens: number;
  compressionRatio: number;
}

export interface CompressionResult {
  compressedChunks: MemoryChunk[];
  removedChunks: MemoryChunk[];
  summary: string;
  tokensSaved: number;
}

export class DynamicMemoryManager {
  private memoryChunks = new Map<string, MemoryChunk>();
  private contextWindow: ContextWindow = {
    activeChunks: [],
    totalTokens: 0,
    maxTokens: 32000, // Conservative limit for GPT-4
    compressionRatio: 0.7 // Target 70% compression when needed
  };
  private lastCompression: Date | null = null;

  constructor(_projectId: string, maxTokens = 32000) {
    this.contextWindow.maxTokens = maxTokens;
  }

  async addMemoryChunk(chunk: Omit<MemoryChunk, 'id' | 'lastAccessed' | 'accessCount' | 'embedding'>): Promise<string> {
    const id = `${chunk.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Generate embedding for semantic search
    const embedding = await this.generateEmbedding(chunk.content);
    
    const memoryChunk: MemoryChunk = {
      ...chunk,
      id,
      lastAccessed: new Date(),
      accessCount: 1,
      embedding
    };

    this.memoryChunks.set(id, memoryChunk);
    
    // Update context window
    await this.updateContextWindow();
    
    return id;
  }

  async updateChunk(id: string, updates: Partial<MemoryChunk>): Promise<boolean> {
    const chunk = this.memoryChunks.get(id);
    if (!chunk) return false;

    const updatedChunk = {
      ...chunk,
      ...updates,
      lastAccessed: new Date(),
      accessCount: chunk.accessCount + 1
    };

    // Regenerate embedding if content changed
    if (updates.content && updates.content !== chunk.content) {
      updatedChunk.embedding = await this.generateEmbedding(updates.content);
    }

    this.memoryChunks.set(id, updatedChunk);
    await this.updateContextWindow();
    
    return true;
  }

  async getRelevantContext(query: string, chapterNumber: number, maxChunks = 20): Promise<MemoryChunk[]> {
    const queryEmbedding = await this.generateEmbedding(query);
    
    // Get all chunks and calculate relevance scores
    const relevantChunks = Array.from(this.memoryChunks.values())
      .filter(chunk => 
        // Include chunks from current and recent chapters
        chunk.chapters.includes(chapterNumber) ||
        chunk.chapters.some(c => Math.abs(c - chapterNumber) <= 3) ||
        // Always include high-importance chunks
        chunk.importance > 0.8
      )
      .map(chunk => ({
        chunk,
        relevanceScore: this.calculateRelevanceScore(chunk, queryEmbedding, chapterNumber)
      }))
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, maxChunks)
      .map(item => {
        // Update access tracking
        item.chunk.lastAccessed = new Date();
        item.chunk.accessCount++;
        return item.chunk;
      });

    return relevantChunks;
  }

  async compressMemory(): Promise<CompressionResult> {
    const allChunks = Array.from(this.memoryChunks.values());
    
    // Sort chunks by importance and recency
    const sortedChunks = allChunks.sort((a, b) => {
      const aScore = this.calculateRetentionScore(a);
      const bScore = this.calculateRetentionScore(b);
      return bScore - aScore;
    });

    const targetChunks = Math.floor(sortedChunks.length * this.contextWindow.compressionRatio);
    const toCompress = sortedChunks.slice(targetChunks);

    // Compress less important chunks into summaries
    const compressionGroups = this.groupChunksForCompression(toCompress);
    const compressedChunks: MemoryChunk[] = [];

    for (const group of compressionGroups) {
      const compressed = await this.compressChunkGroup(group);
      compressedChunks.push(compressed);
    }

    // Update memory with compressed chunks
    toCompress.forEach(chunk => this.memoryChunks.delete(chunk.id));
    compressedChunks.forEach(chunk => this.memoryChunks.set(chunk.id, chunk));

    const tokensSaved = this.estimateTokens(toCompress.map(c => c.content).join(' ')) - 
                       this.estimateTokens(compressedChunks.map(c => c.content).join(' '));

    this.lastCompression = new Date();

    return {
      compressedChunks,
      removedChunks: toCompress,
      summary: `Compressed ${toCompress.length} chunks into ${compressedChunks.length} summaries, saving ~${tokensSaved} tokens`,
      tokensSaved
    };
  }

  async updateContextWindow(): Promise<void> {
    const allChunks = Array.from(this.memoryChunks.values());
    const totalTokens = allChunks.reduce((sum, chunk) => 
      sum + this.estimateTokens(chunk.content), 0
    );

    this.contextWindow = {
      ...this.contextWindow,
      activeChunks: allChunks.filter(chunk => 
        chunk.importance > 0.5 || 
        Date.now() - chunk.lastAccessed.getTime() < 24 * 60 * 60 * 1000 // Last 24 hours
      ),
      totalTokens
    };

    // Trigger compression if memory is getting full
    if (totalTokens > this.contextWindow.maxTokens * 0.8) {
      await this.compressMemory();
    }
  }

  async findSimilarChunks(query: string, limit = 10): Promise<MemoryChunk[]> {
    const queryEmbedding = await this.generateEmbedding(query);
    
    return Array.from(this.memoryChunks.values())
      .filter(chunk => chunk.embedding)
      .map(chunk => ({
        chunk,
        similarity: this.cosineSimilarity(queryEmbedding, chunk.embedding!)
      }))
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(item => item.chunk);
  }

  async mergeSimilarChunks(threshold = 0.9): Promise<number> {
    const chunks = Array.from(this.memoryChunks.values());
    let mergedCount = 0;

    for (let i = 0; i < chunks.length; i++) {
      for (let j = i + 1; j < chunks.length; j++) {
        const chunkA = chunks[i];
        const chunkB = chunks[j];
        
        if (!chunkA || !chunkB) continue;

        if (chunkA.type === chunkB.type && 
            chunkA.embedding && chunkB.embedding &&
            this.cosineSimilarity(chunkA.embedding, chunkB.embedding) > threshold) {
          
          // Merge the chunks
          const mergedChunk = await this.mergeChunks(chunkA, chunkB);
          this.memoryChunks.set(chunkA.id, mergedChunk);
          this.memoryChunks.delete(chunkB.id);
          
          // Remove chunkB from the array to avoid processing it again
          chunks.splice(j, 1);
          j--;
          mergedCount++;
        }
      }
    }

    return mergedCount;
  }

  getMemoryStats(): {
    totalChunks: number;
    totalTokens: number;
    chunksByType: Record<string, number>;
    averageImportance: number;
    compressionRatio: number;
    lastCompression: Date | null;
  } {
    const chunks = Array.from(this.memoryChunks.values());
    const chunksByType = chunks.reduce((acc, chunk) => {
      acc[chunk.type] = (acc[chunk.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalChunks: chunks.length,
      totalTokens: this.contextWindow.totalTokens,
      chunksByType,
      averageImportance: chunks.reduce((sum, c) => sum + c.importance, 0) / chunks.length,
      compressionRatio: this.contextWindow.compressionRatio,
      lastCompression: this.lastCompression
    };
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text.substring(0, 8000) // Limit text length
      });
      const embedding = response.data[0]?.embedding;
      if (!embedding) {
        throw new Error('No embedding returned');
      }
      return embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      return new Array(1536).fill(0); // Return zero vector as fallback
    }
  }

  private calculateRelevanceScore(chunk: MemoryChunk, queryEmbedding: number[], chapterNumber: number): number {
    let score = chunk.importance * 0.4; // Base importance

    // Semantic similarity
    if (chunk.embedding) {
      score += this.cosineSimilarity(queryEmbedding, chunk.embedding) * 0.3;
    }

    // Recency bonus
    const daysSinceAccess = (Date.now() - chunk.lastAccessed.getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, (7 - daysSinceAccess) / 7) * 0.15; // Decay over 7 days

    // Chapter proximity
    const chapterDistance = Math.min(...chunk.chapters.map(c => Math.abs(c - chapterNumber)));
    score += Math.max(0, (5 - chapterDistance) / 5) * 0.15; // Bonus for nearby chapters

    return Math.min(1, score);
  }

  private calculateRetentionScore(chunk: MemoryChunk): number {
    let score = chunk.importance * 0.5;
    
    // Access frequency bonus
    score += Math.min(chunk.accessCount / 10, 0.2);
    
    // Recency bonus
    const daysSinceAccess = (Date.now() - chunk.lastAccessed.getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, (14 - daysSinceAccess) / 14) * 0.3;

    return score;
  }

  private groupChunksForCompression(chunks: MemoryChunk[]): MemoryChunk[][] {
    const groups: MemoryChunk[][] = [];
    const visited = new Set<string>();

    chunks.forEach(chunk => {
      if (visited.has(chunk.id)) return;

      const group = [chunk];
      visited.add(chunk.id);

      // Find similar chunks for grouping
      chunks.forEach(otherChunk => {
        if (!visited.has(otherChunk.id) && 
            chunk.type === otherChunk.type &&
            chunk.chapters.some(c => otherChunk.chapters.includes(c))) {
          group.push(otherChunk);
          visited.add(otherChunk.id);
        }
      });

      groups.push(group);
    });

    return groups;
  }

  private async compressChunkGroup(chunks: MemoryChunk[]): Promise<MemoryChunk> {
    const combinedContent = chunks.map(c => c.content).join('\n\n');
    
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'Compress the following story information into a concise summary that preserves the most important details. Maintain key facts, character details, plot points, and relationships.'
          },
          {
            role: 'user',
            content: combinedContent
          }
        ],
        max_tokens: Math.min(500, Math.floor(combinedContent.length * 0.3))
      });

      const compressedContent = response.choices[0]?.message?.content || '';
      const embedding = await this.generateEmbedding(compressedContent);

      const firstChunk = chunks[0];
      if (!firstChunk) {
        throw new Error('No chunks to compress');
      }
      
      return {
        id: `compressed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: firstChunk.type,
        content: compressedContent,
        importance: Math.max(...chunks.map(c => c.importance)),
        lastAccessed: new Date(),
        accessCount: chunks.reduce((sum, c) => sum + c.accessCount, 0),
        chapters: Array.from(new Set(chunks.flatMap(c => c.chapters))),
        keywords: Array.from(new Set(chunks.flatMap(c => c.keywords))),
        embedding,
        dependencies: Array.from(new Set(chunks.flatMap(c => c.dependencies)))
      };
    } catch (error) {
      console.error('Error compressing chunk group:', error);
      // Return a simple concatenation if AI compression fails
      const firstChunk = chunks[0];
      if (!firstChunk) {
        throw new Error('No chunks to compress');
      }
      return firstChunk;
    }
  }

  private async mergeChunks(chunkA: MemoryChunk, chunkB: MemoryChunk): Promise<MemoryChunk> {
    const mergedContent = `${chunkA.content}\n\n${chunkB.content}`;
    const embedding = await this.generateEmbedding(mergedContent);

    return {
      ...chunkA,
      content: mergedContent,
      importance: Math.max(chunkA.importance, chunkB.importance),
      accessCount: chunkA.accessCount + chunkB.accessCount,
      chapters: Array.from(new Set([...chunkA.chapters, ...chunkB.chapters])),
      keywords: Array.from(new Set([...chunkA.keywords, ...chunkB.keywords])),
      embedding,
      dependencies: Array.from(new Set([...chunkA.dependencies, ...chunkB.dependencies]))
    };
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      const aVal = a[i] ?? 0;
      const bVal = b[i] ?? 0;
      dotProduct += aVal * bVal;
      normA += aVal * aVal;
      normB += bVal * bVal;
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  async mergeSimilarMemories(similarityThreshold: number = 0.85): Promise<{
    mergedCount: number;
    beforeCount: number;
    afterCount: number;
    tokensSaved: number;
  }> {
    const beforeCount = this.memoryChunks.size;
    const allChunks = Array.from(this.memoryChunks.values());
    const merged = new Set<string>();
    const mergedChunks: MemoryChunk[] = [];
    let tokensSaved = 0;

    // Find similar chunks and merge them
    for (let i = 0; i < allChunks.length; i++) {
      const chunkA = allChunks[i];
      if (!chunkA || merged.has(chunkA.id)) continue;

      const similarChunks: MemoryChunk[] = [];
      
      for (let j = i + 1; j < allChunks.length; j++) {
        const chunkB = allChunks[j];
        if (!chunkB || merged.has(chunkB.id)) continue;

        // Check if chunks are similar enough to merge
        if (
          chunkA.type === chunkB.type && // Same type
          chunkA.embedding && chunkB.embedding && // Both have embeddings
          this.cosineSimilarity(chunkA.embedding, chunkB.embedding) >= similarityThreshold
        ) {
          // Additional checks for merging
          const sharedChapters = chunkA.chapters.filter(c => chunkB.chapters.includes(c));
          const sharedKeywords = chunkA.keywords.filter(k => chunkB.keywords.includes(k));
          
          // Only merge if they share context
          if (sharedChapters.length > 0 || sharedKeywords.length >= 2) {
            similarChunks.push(chunkB);
            merged.add(chunkB.id);
          }
        }
      }

      // If we found similar chunks, merge them
      if (similarChunks.length > 0) {
        let mergedChunk = chunkA;
        const _originalTokens = this.estimateTokens(chunkA.content);
        
        for (const similar of similarChunks) {
          const beforeTokens = this.estimateTokens(mergedChunk.content) + this.estimateTokens(similar.content);
          mergedChunk = await this.mergeChunks(mergedChunk, similar);
          const afterTokens = this.estimateTokens(mergedChunk.content);
          tokensSaved += beforeTokens - afterTokens;
          
          // Remove the merged chunk
          this.memoryChunks.delete(similar.id);
        }
        
        // Update the original chunk with merged content
        this.memoryChunks.set(chunkA.id, mergedChunk);
        mergedChunks.push(mergedChunk);
      }
    }

    // Update context window after merging
    await this.updateContextWindow();

    const afterCount = this.memoryChunks.size;
    
    return {
      mergedCount: beforeCount - afterCount,
      beforeCount,
      afterCount,
      tokensSaved
    };
  }
}