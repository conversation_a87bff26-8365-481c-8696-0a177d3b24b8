#!/bin/bash

# Test script for MCP servers
echo "Testing MCP Servers Configuration"
echo "================================="

# Load nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 20

echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo ""

# Test Claude Desktop config
echo "📁 Checking Claude Desktop configuration..."
CONFIG_FILE="$HOME/.config/claude-desktop/config.json"

if [ ! -f "$CONFIG_FILE" ]; then
  echo "❌ Configuration file not found at $CONFIG_FILE"
  exit 1
fi

echo "✅ Configuration file exists"

# Validate JSON
if python3 -m json.tool "$CONFIG_FILE" > /dev/null 2>&1; then
  echo "✅ JSON syntax valid"
else
  echo "❌ Invalid JSON syntax"
  exit 1
fi

# Check server configurations
echo ""
echo "🔍 MCP Servers configured:"
python3 -c "import json; data=json.load(open('$CONFIG_FILE')); [print(f'   - {k}') for k in data['mcpServers'].keys()]"

echo ""
echo "🧪 Testing MCP server executability..."

# Test Context7
echo "Testing Context7..."
if npx @upstash/context7-mcp --help > /dev/null 2>&1; then
  echo "✅ Context7 MCP server working"
else
  echo "❌ Context7 MCP server failed"
fi

# Test Playwright
echo "Testing Playwright..."
if npx @playwright/mcp --version > /dev/null 2>&1; then
  echo "✅ Playwright MCP server working"
else
  echo "❌ Playwright MCP server failed"
fi

# Test Stripe (expect error for missing arguments, but should load)
echo "Testing Stripe..."
if npx @stripe/mcp 2>&1 | grep -q "Invalid argument\|Error initializing"; then
  echo "✅ Stripe MCP server working (expected argument error)"
else
  echo "❌ Stripe MCP server failed"
fi

# Test Supabase
echo "Testing Supabase..."
if npx @supabase/mcp-server-supabase 2>&1 | grep -q "Unknown option\|TypeError"; then
  echo "✅ Supabase MCP server working (expected option error)"
else
  echo "❌ Supabase MCP server failed"
fi

# Test Sentry
echo "Testing Sentry..."
if npx @sentry/mcp-server 2>&1 | grep -q "Invalid argument\|Usage:"; then
  echo "✅ Sentry MCP server working (expected argument error)"
else
  echo "❌ Sentry MCP server failed"
fi

echo ""
echo "🔧 Environment check..."

# Check for required environment variables (from config)
if python3 -c "import json; data=json.load(open('$CONFIG_FILE')); print(data['mcpServers']['stripe']['env'].get('STRIPE_SECRET_KEY', ''))" | grep -q "sk_"; then
  echo "✅ Stripe API key configured"
else
  echo "❌ Stripe API key missing"
fi

if python3 -c "import json; data=json.load(open('$CONFIG_FILE')); print(data['mcpServers']['supabase']['env'].get('SUPABASE_URL', ''))" | grep -q "supabase.co"; then
  echo "✅ Supabase URL configured"
else
  echo "❌ Supabase URL missing"
fi

if python3 -c "import json; data=json.load(open('$CONFIG_FILE')); print(data['mcpServers']['sentry']['env'].get('SENTRY_AUTH_TOKEN', ''))" | grep -q "sntrys_"; then
  echo "✅ Sentry auth token configured"
else
  echo "❌ Sentry auth token missing"
fi

echo ""
echo "📋 Summary:"
echo "- All MCP servers should now be compatible with Node.js $(node --version)"
echo "- Configuration file is valid JSON"
echo "- Restart Claude Desktop to load the updated configuration"
echo ""
echo "Next steps:"
echo "1. Restart Claude Desktop application"
echo "2. Check if MCP tools appear in Claude Code"
echo "3. Test MCP functionality with actual operations"