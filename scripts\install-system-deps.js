#!/usr/bin/env node

/**
 * System Dependencies Installer
 * Installs required system dependencies for headless browser operations
 * Supports Linux, macOS, and Windows environments
 */

const { execSync } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

const platform = os.platform();
const isCI = process.env.CI === 'true';
const isDevelopment = process.env.NODE_ENV !== 'production';

console.log(`🔧 Installing system dependencies for ${platform}...`);

/**
 * Execute command with proper error handling
 */
function executeCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.warn(`⚠️  ${description} failed: ${error.message}`);
    return false;
  }
}

/**
 * Check if command exists
 */
function commandExists(command) {
  try {
    execSync(`${command} --version`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * Install Linux dependencies
 */
function installLinuxDeps() {
  console.log('🐧 Detected Linux environment');
  
  // Check if we have apt-get (Debian/Ubuntu)
  if (commandExists('apt-get')) {
    const packages = [
      'libnss3',
      'libatk-bridge2.0-0', 
      'libdrm2',
      'libgtk-3-0',
      'libgbm1',
      'libasound2',
      'libxss1',
      'libgconf-2-4',
      'libxrandr2',
      'libxcomposite1',
      'libxdamage1',
      'libxfixes3',
      'libxtst6',
      'libatspi2.0-0',
      'libx11-xcb1',
      'libxcb-dri3-0'
    ];
    
    const command = `apt-get update && apt-get install -y ${packages.join(' ')}`;
    
    if (process.getuid && process.getuid() === 0) {
      // Running as root
      return executeCommand(command, 'Installing Linux system dependencies');
    } else {
      // Try with sudo
      return executeCommand(`sudo ${command}`, 'Installing Linux system dependencies (with sudo)');
    }
  }
  
  // Check if we have yum (RHEL/CentOS/Fedora)
  if (commandExists('yum')) {
    const packages = [
      'nss',
      'atk',
      'at-spi2-atk',
      'gtk3',
      'libdrm',
      'libXScrnSaver',
      'libXrandr',
      'GConf2',
      'alsa-lib'
    ];
    
    const command = `yum install -y ${packages.join(' ')}`;
    return executeCommand(`sudo ${command}`, 'Installing Linux system dependencies (yum)');
  }
  
  // Check if we have dnf (newer Fedora)
  if (commandExists('dnf')) {
    const packages = [
      'nss',
      'atk',
      'at-spi2-atk',
      'gtk3',
      'libdrm',
      'libXScrnSaver',
      'libXrandr',
      'GConf2',
      'alsa-lib'
    ];
    
    const command = `dnf install -y ${packages.join(' ')}`;
    return executeCommand(`sudo ${command}`, 'Installing Linux system dependencies (dnf)');
  }
  
  console.warn('⚠️  No supported package manager found (apt-get, yum, or dnf)');
  return false;
}

/**
 * Install macOS dependencies
 */
function installMacOSDeps() {
  console.log('🍎 Detected macOS environment');
  
  if (!commandExists('brew')) {
    console.log('📦 Installing Homebrew...');
    const brewInstall = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"';
    if (!executeCommand(brewInstall, 'Installing Homebrew')) {
      console.warn('⚠️  Failed to install Homebrew. Please install it manually.');
      return false;
    }
  }
  
  // Install Chrome for Puppeteer (if not already installed)
  return executeCommand('brew install --cask google-chrome', 'Installing Google Chrome');
}

/**
 * Install Windows dependencies
 */
function installWindowsDeps() {
  console.log('🪟 Detected Windows environment');
  
  // On Windows, Puppeteer should work out of the box
  // But we can install Chrome via Chocolatey if available
  if (commandExists('choco')) {
    return executeCommand('choco install googlechrome -y', 'Installing Google Chrome via Chocolatey');
  }
  
  console.log('ℹ️  On Windows, Puppeteer should work with the bundled Chromium');
  console.log('ℹ️  If you encounter issues, please install Google Chrome manually');
  return true;
}

/**
 * Install Puppeteer browser
 */
function installPuppeteerBrowser() {
  const puppeteerPath = path.join(__dirname, '..', 'node_modules', 'puppeteer');
  
  if (fs.existsSync(puppeteerPath)) {
    console.log('🎭 Installing Puppeteer browser...');
    try {
      // Try to install the browser
      const puppeteerInstall = path.join(puppeteerPath, 'install.js');
      if (fs.existsSync(puppeteerInstall)) {
        execSync(`node "${puppeteerInstall}"`, { stdio: 'inherit' });
        console.log('✅ Puppeteer browser installed successfully');
        return true;
      }
    } catch (error) {
      console.warn(`⚠️  Puppeteer browser installation failed: ${error.message}`);
    }
  }
  
  return false;
}

/**
 * Main installation function
 */
function main() {
  console.log('🚀 Starting system dependencies installation...');
  
  let success = true;
  
  // Install platform-specific dependencies
  switch (platform) {
    case 'linux':
      success = installLinuxDeps();
      break;
    case 'darwin':
      success = installMacOSDeps();
      break;
    case 'win32':
      success = installWindowsDeps();
      break;
    default:
      console.warn(`⚠️  Unsupported platform: ${platform}`);
      success = false;
  }
  
  // Install Puppeteer browser
  installPuppeteerBrowser();
  
  if (success) {
    console.log('✅ System dependencies installation completed successfully!');
    
    // Create a marker file to indicate successful installation
    const markerPath = path.join(__dirname, '..', '.system-deps-installed');
    fs.writeFileSync(markerPath, new Date().toISOString());
  } else {
    console.warn('⚠️  Some system dependencies may not have been installed correctly.');
    console.warn('⚠️  PDF generation and headless browser features may not work properly.');
    
    if (!isCI && isDevelopment) {
      console.log('\n📖 Manual installation instructions:');
      console.log('Linux (Ubuntu/Debian): sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libgtk-3-0 libgbm1');
      console.log('macOS: brew install --cask google-chrome');
      console.log('Windows: Install Google Chrome from https://www.google.com/chrome/');
    }
  }
  
  console.log('🏁 System dependencies installation process completed.');
}

// Only run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, installLinuxDeps, installMacOSDeps, installWindowsDeps };
