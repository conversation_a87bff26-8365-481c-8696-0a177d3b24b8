/**
 * Example usage of error handling utilities in API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  withErrorHandling, 
  AppError, 
  createErrorResponse, 
  validateRequired,
  logError 
} from './error-handling';
import { z } from 'zod';

// Example 1: Using withErrorHandling wrapper
export const GET = withErrorHandling(async (_request: NextRequest) => {
  // Your route logic here
  const data = await fetchSomeData();
  return NextResponse.json({ data });
});

// Example 2: Throwing custom errors
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    validateRequired(body, ['title', 'content']);
    
    // Custom validation
    if (body.title.length < 3) {
      throw new AppError(
        'Title must be at least 3 characters long',
        400,
        'INVALID_TITLE',
        { minLength: 3, actualLength: body.title.length }
      );
    }
    
    // Process the request
    const result = await processRequest(body);
    return NextResponse.json({ success: true, result });
    
  } catch (error) {
    // Log error with context
    logError(error, {
      action: 'createPost',
      userId: 'user123',
      metadata: { endpoint: '/api/posts' }
    });
    
    // Return standardized error response
    if (error instanceof AppError) {
      return createErrorResponse('VALIDATION_ERROR', error.message, error.details);
    }
    
    return createErrorResponse('INTERNAL_ERROR');
  }
}

// Example 3: Using Zod validation with error handling
const createProjectSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  settings: z.object({
    genre: z.string(),
    targetWordCount: z.number().positive()
  })
});

export const createProject = withErrorHandling(async (request: NextRequest) => {
  const body = await request.json();
  
  // This will throw ZodError if validation fails
  // withErrorHandling will catch it and return proper error response
  const validatedData = createProjectSchema.parse(body);
  
  const project = await createProjectInDB(validatedData);
  return NextResponse.json({ project });
});

// Helper functions for examples
async function fetchSomeData() {
  // Simulated data fetch
  return { id: 1, name: 'Example' };
}

async function processRequest(data: unknown) {
  // Simulated processing
  return { processed: true, data };
}

async function createProjectInDB(data: unknown) {
  // Simulated DB operation
  return { id: 'proj_123', ...data };
}