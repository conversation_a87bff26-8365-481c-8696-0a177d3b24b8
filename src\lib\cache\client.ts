'use client'

import { useEffect, useState, useCallback } from 'react'
import { Cache } from './server'

// Re-export cache instances and utilities for client use
export { 
  Cache,
  projectCache,
  projectListCache,
  chapterCache,
  characterCache,
  aiResponseCache,
  searchResultsCache,
  cacheKeys,
  invalidate<PERSON><PERSON><PERSON><PERSON>ache,
  invalidateC<PERSON><PERSON>er<PERSON>ache,
  invalidateChara<PERSON><PERSON>ache
} from './server'

// React hook for cached data fetching
interface UseCachedDataOptions<T> {
  cacheKey: string
  cache: Cache<T>
  fetcher: () => Promise<T>
  dependencies?: unknown[]
  staleTime?: number // Time before data is considered stale
}

export function useCachedData<T>({
  cacheKey,
  cache,
  fetcher,
  dependencies = [],
  staleTime = 0,
}: UseCachedDataOptions<T>) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  
  useEffect(() => {
    let cancelled = false
    
    const fetchData = async () => {
      try {
        // Check cache first
        const cached = cache.get(cacheKey)
        if (cached) {
          setData(cached)
          setLoading(false)
          
          // If data is stale, fetch in background
          if (staleTime > 0) {
            const cacheAge = Date.now() - (cached as T & { _cacheTime: number })._cacheTime
            if (cacheAge > staleTime) {
              // Fetch fresh data in background
              fetcher().then(freshData => {
                if (!cancelled) {
                  const dataWithTime = { ...freshData, _cacheTime: Date.now() }
                  cache.set(cacheKey, dataWithTime)
                  setData(dataWithTime)
                }
              }).catch(console.error)
            }
          }
          return
        }
        
        // No cache, fetch fresh data
        setLoading(true)
        const freshData = await fetcher()
        
        if (!cancelled) {
          const dataWithTime = { ...freshData, _cacheTime: Date.now() }
          cache.set(cacheKey, dataWithTime)
          setData(dataWithTime)
          setLoading(false)
        }
      } catch (err) {
        if (!cancelled) {
          setError(err as Error)
          setLoading(false)
        }
      }
    }
    
    fetchData()
    
    return () => {
      cancelled = true
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cacheKey, cache, fetcher, staleTime, ...dependencies])
  
  const refresh = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const freshData = await fetcher()
      const dataWithTime = { ...freshData, _cacheTime: Date.now() }
      cache.set(cacheKey, dataWithTime)
      setData(dataWithTime)
    } catch (err) {
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [fetcher, cacheKey, cache])
  
  return { data, loading, error, refresh }
}