'use client'

import { useState, useEffect } from 'react'
import '@/styles/knowledge-demo.css'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { initializeTheme } from '@/lib/themes/theme-applier'
import {
  Save,
  FileText,
  ArrowLeft,
  BarChart3,
  Brain,
  <PERSON>ather,
  Upload,
  <PERSON>lette,
  Plus,
  CheckCircle,
  Circle,
  Edit3,
  Search,
  List,
  BookOpen,
  Eye,
  X,
  PanelRightClose
} from 'lucide-react'
import Link from 'next/link'

// Demo data
const demoChapters = [
  { id: 'ch1', number: 1, title: 'The Awakening', status: 'completed', wordCount: 2500 },
  { id: 'ch2', number: 2, title: 'Shadows and Whispers', status: 'writing', wordCount: 1200 },
  { id: 'ch3', number: 3, title: 'The Ancient Oak', status: 'planned', wordCount: 0 },
  { id: 'ch4', number: 4, title: 'Secrets Unveiled', status: 'planned', wordCount: 0 },
  { id: 'ch5', number: 5, title: 'The Prophecy', status: 'planned', wordCount: 0 }
]

const sampleContent = `Chapter 1: The Awakening

The old oak tree stood sentinel at the edge of Willowbrook, its gnarled branches reaching toward the storm-darkened sky. <PERSON> approached cautiously, her heart pounding as she remembered her grandmother's warnings about the ancient tree and its mysterious powers.

"Never go near the oak when the moon is full," her grandmother had whispered on her deathbed. "The spirits that dwell within are restless, and they hunger for the living."

But Sarah was no longer the frightened child who had cowered beneath her grandmother's tales. She was a scholar now, armed with knowledge and determination. The leather-bound journal in her hands contained decades of research about the supernatural phenomena that plagued their small town.

As she drew closer to the massive trunk, the wind began to howl through the branches, creating an otherworldly symphony that seemed to call her name. The bark felt warm beneath her palm, pulsing with an energy that made her fingertips tingle.

Suddenly, the world around her began to shift and blur. The familiar landscape of Willowbrook dissolved into something far more ancient and mysterious. She found herself standing in a moonlit grove where the very air shimmered with magic.

"You have come at last," a voice whispered from the shadows between the trees. Sarah spun around, searching for the source, but saw only dancing shadows and silver light filtering through leaves that seemed to glow with their own inner fire.

The journal in her hands grew warm, its pages fluttering open of their own accord. Words began to appear on the yellowed parchment, written in a script she had never seen before but somehow understood perfectly.

"The time of choosing has arrived. Will you embrace your heritage, or will you turn away as your mother did before you?"

Sarah's breath caught in her throat. Her mother had never spoken of any heritage beyond their simple family history. But as she stared at the mysterious words, memories began to surface—fragments of conversations overheard in childhood, strange dreams that had haunted her sleep, and the inexplicable pull she had always felt toward the old oak.

The wind picked up, swirling around her with increasing intensity. The journal's pages turned rapidly, revealing more secrets with each flutter. Sarah realized she stood at a crossroads, not just in this mystical grove, but in her very existence.

Whatever choice she made in this moment would change everything.`

export default function KnowledgeDemoPage() {
  const [showRightPanel, setShowRightPanel] = useState(true)
  const [showChapterNavigator, setShowChapterNavigator] = useState(true)
  const [showWritingStats, setShowWritingStats] = useState(false)
  const [currentChapterId, setCurrentChapterId] = useState('ch1')
  const [content, setContent] = useState(sampleContent)
  const [isSaving, setIsSaving] = useState(false)

  // Initialize theme
  useEffect(() => {
    // Ensure Writer's Sanctuary theme is applied
    initializeTheme()
  }, [])
  
  // Simple demo functions
  const toggleChapterNavigator = () => setShowChapterNavigator(!showChapterNavigator)
  
  // Demo save function
  const saveChapter = () => {
    setIsSaving(true)
    setTimeout(() => {
      setIsSaving(false)
      alert("Demo: Chapter saved (no actual saving occurs)")
    }, 1000)
  }

  const handleChapterSelect = (id: string) => {
    setCurrentChapterId(id)
    alert(`Demo: Switched to ${demoChapters.find(ch => ch.id === id)?.title}`)
  }

  const handleCreateChapter = () => {
    alert("Demo: Chapters cannot be created in demo mode")
  }
  
  return (
    <div className="h-screen flex flex-col bg-background text-foreground knowledge-demo-container">
      {/* Header */}
      <header className="border-b border-border bg-card px-4 py-2 flex items-center justify-between demo-header">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>

          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Feather className="h-5 w-5 text-primary" />
              <h1 className="text-lg font-semibold text-foreground">
                Knowledge Panel Demo
              </h1>
            </div>
            <Badge variant="outline" className="ml-2">
              Interactive Demo
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Link href="/customization">
            <Button variant="ghost" size="sm">
              <Palette className="h-4 w-4" />
            </Button>
          </Link>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleChapterNavigator}
            className={showChapterNavigator ? 'bg-primary/10 text-primary' : ''}
          >
            <FileText className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRightPanel(!showRightPanel)}
            className={showRightPanel ? 'bg-primary/10 text-primary' : ''}
          >
            <Brain className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowWritingStats(!showWritingStats)}
            className={showWritingStats ? 'bg-primary/10 text-primary' : ''}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>

          <Button
            onClick={saveChapter}
            disabled={isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chapter Navigator */}
        {showChapterNavigator && (
          <div className="w-64 border-r border-border bg-card chapter-navigator">
            <div className="h-full flex flex-col">
              {/* Chapter Navigator Header */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold font-literary text-card-foreground">Chapters</h2>
                  <Button variant="ghost" size="sm" onClick={handleCreateChapter}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Chapter List */}
              <div className="flex-1 overflow-y-auto p-2">
                {demoChapters.map((chapter) => (
                  <div
                    key={chapter.id}
                    className={`p-3 rounded-lg mb-2 cursor-pointer transition-colors ${
                      currentChapterId === chapter.id
                        ? 'bg-primary/10 border border-primary/20'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleChapterSelect(chapter.id)}
                  >
                    <div className="flex items-center gap-2">
                      {chapter.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                      {chapter.status === 'writing' && <Edit3 className="h-4 w-4 text-yellow-500" />}
                      {chapter.status === 'planned' && <Circle className="h-4 w-4 text-gray-400" />}
                      <span className="font-medium text-card-foreground">
                        Chapter {chapter.number}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {chapter.title}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {chapter.wordCount} words
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex bg-background">
          {/* Editor */}
          <div className="flex-1 bg-background">
            <div className="h-full p-4">
              <div className="h-full bg-card rounded-lg border border-border p-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Loading...
                </div>
                <div className="prose prose-sm max-w-none text-card-foreground">
                  <div className="whitespace-pre-wrap font-mono text-sm">
                    {content}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel */}
          {showRightPanel && (
            <div className="w-96 border-l border-border bg-card sidebar-panel">
              <div className="h-full flex flex-col">
                {/* Panel Header */}
                <div className="p-4 border-b border-border">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold font-literary text-card-foreground">Project Tools</h2>
                    <Button variant="ghost" size="sm" onClick={() => setShowRightPanel(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Panel Content */}
                <div className="flex-1 p-4">
                  <div className="space-y-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h3 className="font-semibold text-card-foreground mb-2">Knowledge Base</h3>
                      <p className="text-sm text-muted-foreground">
                        Select text in the editor to capture it here.
                      </p>
                    </div>

                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h3 className="font-semibold text-card-foreground mb-2">AI Assistant</h3>
                      <p className="text-sm text-muted-foreground">
                        AI chat and suggestions would appear here.
                      </p>
                    </div>

                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h3 className="font-semibold text-card-foreground mb-2">Story Bible</h3>
                      <p className="text-sm text-muted-foreground">
                        Characters, locations, and plot elements.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Instructions Overlay */}
      <div className="fixed bottom-6 left-6 max-w-md z-50">
        <div className="bg-card/95 backdrop-blur-sm rounded-lg shadow-lg border border-border p-4 instructions-overlay">
          <h3 className="font-semibold text-card-foreground mb-2 font-literary demo-text">
            Knowledge Panel Demo Instructions:
          </h3>
          <ul className="text-sm text-muted-foreground space-y-1 demo-muted-text">
            <li>• Select text in the editor to capture it in the Knowledge panel</li>
            <li>• Use the right panel tabs to switch between Knowledge, AI Chat, Story Bible, and more</li>
            <li>• Toggle panels using the layout manager in the header</li>
            <li>• Resize panels by dragging their edges</li>
            <li>• Try different layout presets from the Layout dropdown</li>
            <li>• This is a fully interactive demo with the complete editor interface</li>
          </ul>
        </div>
      </div>
    </div>
  )
}